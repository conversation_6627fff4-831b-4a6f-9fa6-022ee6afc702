"use client";

import React from "react";

interface SkipLink {
  href: string;
  label: string;
}

interface SkipLinksProps {
  links?: SkipLink[];
  className?: string;
}

const defaultLinks: SkipLink[] = [
  { href: "#main-content", label: "Skip to main content" },
  { href: "#navigation", label: "Skip to navigation" },
  { href: "#footer", label: "Skip to footer" },
];

export const SkipLinks: React.FC<SkipLinksProps> = ({ 
  links = defaultLinks, 
  className = "" 
}) => {
  const handleSkipLinkClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
    const href = event.currentTarget.getAttribute("href");
    if (!href) return;

    event.preventDefault();
    
    // Find the target element
    const targetElement = document.querySelector(href);
    if (targetElement) {
      // Ensure the element is focusable
      const originalTabIndex = targetElement.getAttribute("tabindex");
      if (!originalTabIndex && originalTabIndex !== "0") {
        targetElement.setAttribute("tabindex", "-1");
      }
      
      // Focus the element
      (targetElement as HTMLElement).focus();
      
      // Scroll to the element
      targetElement.scrollIntoView({ 
        behavior: "smooth", 
        block: "start" 
      });
      
      // Remove temporary tabindex after a short delay
      if (!originalTabIndex && originalTabIndex !== "0") {
        setTimeout(() => {
          targetElement.removeAttribute("tabindex");
        }, 100);
      }
    }
  };

  return (
    <nav aria-label="Skip navigation links" className={className}>
      {links.map((link, index) => (
        <a
          key={index}
          href={link.href}
          className="skip-links"
          onClick={handleSkipLinkClick}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleSkipLinkClick(e as any);
            }
          }}
        >
          {link.label}
        </a>
      ))}
    </nav>
  );
};

export default SkipLinks;
